/* google font  */
@import url('https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Dosis:wght@200..800&display=swap');

body{
     font-family: 'Raleway';
     padding: 0;
     margin: 0;
}
.container{
width: 80%;
margin: auto;;
 }

.navbar{
display:flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
height: 30px;
    margin-top: 30px;

}

.unorderlist ul{
    display: flex;
    list-style-type: none;
     
 }
.unorderlist ul li{
    padding: 10px;
     position: relative; /* for orange line under the link */
}
/* for orange line under the links */
.unorderlist ul li::after{
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: teal;
    transition: width 0.3s ease;
}
.unorderlist ul li:hover::after{
    width: 100%;
}
.unorderlist ul li  a{
 padding: 10px 15px; 
 color: black;
 text-transform: capitalize;
 font-family: 'Ralway';
    text-decoration: none;
    font-size: large;
}
.unorderlist ul li.active::after{
width: 100%;
 }

.navbar img{
 }

 
   .navbar .hireme {
       background-color: teal;
       border-radius:100px;
       padding: 7px 15px;
       
    }
 
   .navbar .hireme a {
    color: white;
    text-decoration: none;
    text-transform: capitalize;
    font-size: large;
    font-weight: bold;
   }

   .navbar .hireme i{
    background: white;
    border-radius: 100%;
    padding: 5px;
   }
   .navbar .hireme i  {
    transition: 1s;
   }
   .navbar .hireme i:hover{
    transform: rotate(360deg);
    background-color: coral;
    color: white;
   }

   /* end navbar */

   /* start header */

   .header{
    height: 700px;
        color:white;
    background-image: url(../image//header//mrAhmad.jpeg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
      background-attachment: fixed;
     display: flex;
    align-items: center;
    justify-content: left;
     margin-top: 30px;
   }
 .header-content{
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: left;
    padding-right: 10px !important;

 }
 .overlay{
    width:100%;
    padding: 20px;
 }
   .overlay span {
    color: white;
    font-size: 23px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 10px;
   }
.header-content h1{
       font-size: 60px;
    font-weight: 700;
    text-transform: uppercase;
}
.header-content > * {
  padding-right: 10px;
}
.header p {
        font-family: 'Raleway', sans-serif;
    font-size: 20px;
    line-height: 1.5;
    margin-bottom: 20px;
}
.header .social{
    display: flex;
    flex-direction:row;
    
 }
 .header .social .icon{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
  }

 .header .social .icon i {
border: 1px solid #3b5999;
    border-radius: 100%;
    padding: 15px;
    color:#3b5999;
    font-size: 25px;
    transition: 1s;
 }
 .header .social .icon i:hover{
    color: white;
    background-color: teal;
    border-color: white;
    transform: rotate(360deg);
 }

 /* end header */

 /* start about us */
 .aboutus{
    background-color: #f5f5f5;
         height:530px;
        

 }
 .aboutus .box{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
 }
 .aboutus .achevements {
    flex: 40%;
    max-width: 40%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    background-color: #f9f9f9;
    padding: 30px 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
  .aboutus .achevements span {
    color: #333;
    background-color: white;
    border: 1px solid #ddd;
    font-family: 'Raleway', sans-serif;
    font-weight: 600;
    font-size: 14px;
    border-radius: 30px;
    padding: 8px 15px;
    margin: 8px;
    display: inline-block;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.09);
    transition: all 0.3s ease;
}
  .aboutus .achevements span:hover {
    background-color: coral;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 5px rgba(0,0,0,0.1);
}
  .aboutus .aboutus-text{
    flex:60%;
        max-width: 60%;

     background-color: teal;
 
   }
  .aboutus .achevements h3 {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-family: 'Raleway', sans-serif;
  }
  .aboutus .achevements .skills-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .aboutus .aboutus-text{
    padding:3rem;

  }
    .aboutus .aboutus-text span{
        text-transform: uppercase;
        border-bottom:2px solid coral ;
    }
    .aboutus .aboutus-text h1  {
        color:white;

        font-size: 48px;
    font-weight: 700;
    margin-left: 20px;
    margin-bottom: 18px;
    }
    .aboutus .aboutus-text p {
        color: #ffffff;
        font-family: 'Raleway', sans-serif;
        font-size: 16px;
        line-height: 1.8;
        margin-bottom: 20px;
        text-align: justify;
        padding: 0 20px;
        font-weight: 400;
        letter-spacing: 0.5px;
        max-width: 90%;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        position: relative;
        padding-left: 20px;
        border-left: 3px solid coral;
    }
    .aboutus .aboutus-text p::first-letter {
        font-size: 24px;
        font-weight: 700;
        color: coral;
        margin-top: 900px;
    }
 
    /* end about us */

    /* start what i do */
  
      .whatIdo{
      }
      .box-whatIdo{
      border-top:3px solid coral;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
    padding: 50px 0px;
    }




    .services{
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
         margin-top: 50px;
        text-align:center; 
    }
        .services .service{
            flex: 25%;
            margin: 1px;
        }
    .services .service h2{
         text-transform: capitalize;
    color: #3b3b3b;
    font-size: 20px;
    font-weight: 500;
    margin-top: 30px;
    margin-bottom: 20px;
    }

    .services .service p{
   color: #818181;
    font-weight: 400;
    margin-bottom: 30px;
    }